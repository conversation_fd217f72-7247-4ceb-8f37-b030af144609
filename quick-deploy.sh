#!/bin/bash

# 一键部署脚本
# 自动安装和配置项目运行环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -eq 0 ]; then
        log_error "请不要使用root用户运行此脚本"
        exit 1
    fi
}

# 检测操作系统
detect_os() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    else
        log_error "无法检测操作系统"
        exit 1
    fi
    
    log_info "检测到操作系统: $OS $VER"
}

# 安装Docker
install_docker() {
    log_info "检查Docker安装状态..."
    
    if command -v docker &> /dev/null; then
        log_success "Docker已安装"
        return 0
    fi
    
    log_info "开始安装Docker..."
    
    case $OS in
        *"Ubuntu"*|*"Debian"*)
            # 更新包索引
            sudo apt update
            
            # 安装必要的包
            sudo apt install -y apt-transport-https ca-certificates curl gnupg lsb-release
            
            # 添加Docker官方GPG密钥
            curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
            
            # 添加Docker仓库
            echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
            
            # 安装Docker
            sudo apt update
            sudo apt install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
            ;;
        *"CentOS"*|*"Red Hat"*)
            # 安装必要的包
            sudo yum install -y yum-utils
            
            # 添加Docker仓库
            sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
            
            # 安装Docker
            sudo yum install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
            ;;
        *)
            log_error "不支持的操作系统: $OS"
            exit 1
            ;;
    esac
    
    # 启动Docker服务
    sudo systemctl start docker
    sudo systemctl enable docker
    
    # 将当前用户添加到docker组
    sudo usermod -aG docker $USER
    
    log_success "Docker安装完成"
    log_warning "请重新登录以使docker组权限生效"
}

# 安装其他必要软件
install_dependencies() {
    log_info "安装其他必要软件..."
    
    case $OS in
        *"Ubuntu"*|*"Debian"*)
            sudo apt update
            sudo apt install -y git curl wget nginx certbot python3-certbot-nginx
            ;;
        *"CentOS"*|*"Red Hat"*)
            sudo yum install -y git curl wget nginx certbot python3-certbot-nginx
            ;;
    esac
    
    log_success "依赖软件安装完成"
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙..."
    
    if command -v ufw &> /dev/null; then
        # Ubuntu/Debian使用ufw
        sudo ufw --force enable
        sudo ufw allow ssh
        sudo ufw allow http
        sudo ufw allow https
        log_success "UFW防火墙配置完成"
    elif command -v firewall-cmd &> /dev/null; then
        # CentOS/RHEL使用firewalld
        sudo systemctl start firewalld
        sudo systemctl enable firewalld
        sudo firewall-cmd --permanent --add-service=ssh
        sudo firewall-cmd --permanent --add-service=http
        sudo firewall-cmd --permanent --add-service=https
        sudo firewall-cmd --reload
        log_success "Firewalld防火墙配置完成"
    else
        log_warning "未检测到防火墙，请手动配置"
    fi
}

# 克隆项目
clone_project() {
    local repo_url=$1
    local project_dir="postgresql-vue-project"
    
    if [ -z "$repo_url" ]; then
        log_error "请提供Git仓库URL"
        exit 1
    fi
    
    log_info "克隆项目代码..."
    
    if [ -d "$project_dir" ]; then
        log_warning "项目目录已存在，正在更新..."
        cd "$project_dir"
        git pull
    else
        git clone "$repo_url" "$project_dir"
        cd "$project_dir"
    fi
    
    log_success "项目代码准备完成"
}

# 配置环境变量
configure_environment() {
    log_info "配置环境变量..."
    
    if [ ! -f .env ]; then
        cp .env.example .env
        log_warning "已创建.env文件，请编辑其中的配置"
        
        # 生成随机密钥
        local secret_key=$(openssl rand -hex 32)
        local jwt_key=$(openssl rand -hex 32)
        local db_password=$(openssl rand -base64 32)
        
        # 更新.env文件
        sed -i "s/your_strong_password_here/$db_password/g" .env
        sed -i "s/your_very_strong_secret_key_here/$secret_key/g" .env
        sed -i "s/your_jwt_secret_key_here/$jwt_key/g" .env
        
        log_success "环境变量配置完成"
    else
        log_info ".env文件已存在，跳过配置"
    fi
}

# 部署项目
deploy_project() {
    log_info "开始部署项目..."
    
    # 给部署脚本执行权限
    chmod +x deploy.sh
    
    # 构建镜像
    log_info "构建Docker镜像..."
    ./deploy.sh build
    
    # 启动服务
    log_info "启动服务..."
    ./deploy.sh up
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    ./deploy.sh health
    
    log_success "项目部署完成"
}

# 显示部署结果
show_result() {
    local server_ip=$(curl -s ifconfig.me || echo "未知")
    
    echo ""
    echo "=================================="
    echo "🎉 部署完成！"
    echo "=================================="
    echo ""
    echo "服务器信息："
    echo "  IP地址: $server_ip"
    echo "  前端访问: http://$server_ip"
    echo "  后端API: http://$server_ip:5000/api"
    echo ""
    echo "默认管理员账号："
    echo "  用户名: admin"
    echo "  密码: 123456"
    echo ""
    echo "下一步操作："
    echo "1. 配置域名DNS解析到服务器IP"
    echo "2. 运行SSL配置脚本获取HTTPS证书"
    echo "3. 修改默认管理员密码"
    echo "4. 配置数据库备份策略"
    echo ""
    echo "有用的命令："
    echo "  查看服务状态: ./deploy.sh health"
    echo "  查看日志: ./deploy.sh logs"
    echo "  重启服务: ./deploy.sh restart"
    echo "  备份数据库: ./database/backup.sh full"
    echo ""
}

# 主函数
main() {
    local repo_url=$1
    
    echo "=================================="
    echo "🚀 PostgreSQL-Vue项目一键部署脚本"
    echo "=================================="
    echo ""
    
    # 检查参数
    if [ -z "$repo_url" ]; then
        echo "使用方法: $0 <Git仓库URL>"
        echo "示例: $0 https://github.com/username/postgresql-vue-project.git"
        exit 1
    fi
    
    # 执行部署步骤
    check_root
    detect_os
    install_docker
    install_dependencies
    configure_firewall
    clone_project "$repo_url"
    configure_environment
    deploy_project
    show_result
    
    log_success "一键部署完成！"
}

# 执行主函数
main "$@"
