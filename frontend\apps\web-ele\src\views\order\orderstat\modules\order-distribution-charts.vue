<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { <PERSON><PERSON><PERSON>, <PERSON>Row, ElCol } from 'element-plus';
import * as echarts from 'echarts';
import type { OrderDistributionData } from '../../../../api/order-stat';

defineOptions({ name: 'OrderDistributionCharts' });

interface Props {
  distributionData: OrderDistributionData | null;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

// 图表实例引用
const orderChartRef = ref<HTMLDivElement>();
const shopChartRef = ref<HTMLDivElement>();
const profitChartRef = ref<HTMLDivElement>();
let orderChart: echarts.ECharts | null = null;
let shopChart: echarts.ECharts | null = null;
let profitChart: echarts.ECharts | null = null;

// 计算店铺订单分布数据（基于shopDistribution的count字段）
const orderDistributionData = computed(() => {
  if (!props.distributionData?.shopDistribution) {
    return [];
  }
  return props.distributionData.shopDistribution.map(item => ({
    name: item.name,
    value: item.count,
    count: item.count
  }));
});

// 计算店铺分布数据
const shopDistributionData = computed(() => {
  if (!props.distributionData?.shopDistribution) {
    return [];
  }
  return props.distributionData.shopDistribution;
});

// 计算利润分布数据
const profitDistributionData = computed(() => {
  if (!props.distributionData?.profitDistribution) {
    return [];
  }
  return props.distributionData.profitDistribution;
});

// 创建南丁格尔玫瑰图配置
function createRoseChartOption(data: any[], title: string, isOrderCount = false) {
  return {
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        const value = isOrderCount ? params.value : Number(params.value).toFixed(2);
        const percent = Number(params.percent).toFixed(2);
        const unit = isOrderCount ? '单' : '¥';
        return `${params.seriesName} <br/>${params.name}: ${unit}${value} (${percent}%)`;
      }
    },
    legend: {
      orient: 'vertical',
      left: 'right',
      top: 'center',
      textStyle: {
        color: '#333',
        textBorderColor: '#fff',
        textBorderWidth: 1
      }
    },
    series: [
      {
        name: title,
        type: 'pie',
        radius: ['20%', '70%'],
        center: ['40%', '50%'],
        roseType: 'area',
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 8,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        data: data,
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold'
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };
}

// 初始化订单分布图表
function initOrderChart() {
  if (!orderChartRef.value) return;

  orderChart = echarts.init(orderChartRef.value);
  updateOrderChart();
}

// 初始化店铺分布图表
function initShopChart() {
  if (!shopChartRef.value) return;

  shopChart = echarts.init(shopChartRef.value);
  updateShopChart();
}

// 初始化利润分布图表
function initProfitChart() {
  if (!profitChartRef.value) return;

  profitChart = echarts.init(profitChartRef.value);
  updateProfitChart();
}

// 更新订单分布图表
function updateOrderChart() {
  if (!orderChart) return;

  const option = createRoseChartOption(orderDistributionData.value, '店铺订单分布', true);
  orderChart.setOption(option);
}

// 更新店铺分布图表
function updateShopChart() {
  if (!shopChart) return;

  const option = createRoseChartOption(shopDistributionData.value, '店铺销售分布');
  shopChart.setOption(option);
}

// 更新利润分布图表
function updateProfitChart() {
  if (!profitChart) return;

  const option = createRoseChartOption(profitDistributionData.value, '店铺利润分布');
  profitChart.setOption(option);
}

// 监听数据变化
watch(() => props.distributionData, () => {
  updateOrderChart();
  updateShopChart();
  updateProfitChart();
}, { deep: true });

// 监听loading状态
watch(() => props.loading, (loading) => {
  if (orderChart) {
    if (loading) {
      orderChart.showLoading();
    } else {
      orderChart.hideLoading();
    }
  }
  if (shopChart) {
    if (loading) {
      shopChart.showLoading();
    } else {
      shopChart.hideLoading();
    }
  }
  if (profitChart) {
    if (loading) {
      profitChart.showLoading();
    } else {
      profitChart.hideLoading();
    }
  }
});

// 窗口大小变化时重新调整图表
function handleResize() {
  orderChart?.resize();
  shopChart?.resize();
  profitChart?.resize();
}

onMounted(() => {
  // 延迟初始化，确保DOM已渲染
  setTimeout(() => {
    initOrderChart();
    initShopChart();
    initProfitChart();
  }, 100);

  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  orderChart?.dispose();
  shopChart?.dispose();
  profitChart?.dispose();
  window.removeEventListener('resize', handleResize);
});
</script>

<template>
  <div class="distribution-charts">
    <ElRow :gutter="16">
      <!-- 店铺订单分布 - 第一个 -->
      <ElCol :xs="24" :sm="24" :md="8" :lg="8">
        <ElCard class="chart-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">店铺订单分布</span>
            </div>
          </template>
          <div
            ref="orderChartRef"
            class="chart-container"
            v-loading="loading"
          ></div>
        </ElCard>
      </ElCol>
      <!-- 店铺销售分布 -->
      <ElCol :xs="24" :sm="24" :md="8" :lg="8">
        <ElCard class="chart-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">店铺销售分布</span>
            </div>
          </template>
          <div
            ref="shopChartRef"
            class="chart-container"
            v-loading="loading"
          ></div>
        </ElCard>
      </ElCol>
      <!-- 店铺利润分布 -->
      <ElCol :xs="24" :sm="24" :md="8" :lg="8">
        <ElCard class="chart-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">店铺利润分布</span>
            </div>
          </template>
          <div
            ref="profitChartRef"
            class="chart-container"
            v-loading="loading"
          ></div>
        </ElCard>
      </ElCol>
    </ElRow>
  </div>
</template>

<style scoped>
.distribution-charts {
  width: 100%;
}

.chart-card {
  border-radius: 12px;
  border: 1px solid hsl(var(--border));
  transition: all 0.3s ease;
}

.chart-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.chart-card :deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid hsl(var(--border));
  background-color: hsl(var(--muted) / 0.3);
}

.chart-card :deep(.el-card__body) {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: hsl(var(--foreground));
}

.chart-container {
  width: 100%;
  height: 400px;
  padding: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-container {
    height: 350px;
    padding: 16px;
  }
  
  .card-title {
    font-size: 15px;
  }
  
  .chart-card :deep(.el-card__header) {
    padding: 12px 16px;
  }
}

@media (max-width: 480px) {
  .chart-container {
    height: 300px;
    padding: 12px;
  }
  
  .card-title {
    font-size: 14px;
  }
  
  .chart-card :deep(.el-card__header) {
    padding: 10px 12px;
  }
}

/* 夜间模式适配 */
@media (prefers-color-scheme: dark) {
  .chart-card {
    background-color: hsl(var(--card));
    border-color: hsl(var(--border));
  }
  
  .chart-card:hover {
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
  }
}
</style>
