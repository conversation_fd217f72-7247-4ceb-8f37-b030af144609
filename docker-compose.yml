version: '3.8'

services:
  # PostgreSQL数据库
  database:
    image: postgres:15-alpine
    container_name: postgresql_vue_db
    restart: unless-stopped
    environment:
      POSTGRES_DB: postgresql_vue_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${DB_PASSWORD:-123456}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/database/init_database.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5432:5432"
    networks:
      - app-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: postgresql_vue_backend
    restart: unless-stopped
    environment:
      - FLASK_ENV=production
      - DB_HOST=database
      - DB_PORT=5432
      - DB_NAME=postgresql_vue_db
      - DB_USER=postgres
      - DB_PASSWORD=${DB_PASSWORD:-123456}
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-jwt-secret-key}
    volumes:
      - backend_uploads:/app/static/uploads
      - backend_storage:/app/storage
    ports:
      - "5000:5000"
    networks:
      - app-network
    depends_on:
      database:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: postgresql_vue_frontend
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    networks:
      - app-network
    depends_on:
      - backend
    volumes:
      - ./ssl:/etc/nginx/ssl:ro  # SSL证书目录（可选）

  # Redis缓存（可选，用于会话存储）
  redis:
    image: redis:7-alpine
    container_name: postgresql_vue_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    networks:
      - app-network
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

networks:
  app-network:
    driver: bridge

volumes:
  postgres_data:
    driver: local
  backend_uploads:
    driver: local
  backend_storage:
    driver: local
  redis_data:
    driver: local
