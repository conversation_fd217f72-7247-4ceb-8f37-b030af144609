#!/usr/bin/env python3
"""重新初始化数据库"""

try:
    import psycopg2
    import psycopg2.extras
except ImportError:
    print("❌ psycopg2 模块未找到！")
    print("请在虚拟环境中安装 psycopg2:")
    print("  pip install psycopg2-binary")
    print("或者:")
    print("  pip install psycopg2")
    exit(1)
import os
import sys
from datetime import datetime
import bcrypt

# 添加父目录到路径，以便导入config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.config import Config

def hash_password(password):
    """密码加密"""
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')



def init_database():
    """初始化数据库"""
    print("=== 重新初始化PostgreSQL数据库 ===\n")

    conn = None
    try:
        # 连接到PostgreSQL数据库
        print("连接到PostgreSQL数据库...")
        conn = psycopg2.connect(**Config.DATABASE_CONFIG)
        conn.autocommit = True  # 启用自动提交，用于DROP/CREATE操作
        cursor = conn.cursor()

        # 删除现有表（如果存在）
        print("删除现有表...")
        tables_to_drop = [
            'user_roles', 'role_permissions', 'permissions', 'roles', 'users',
            'orderlist', 'paymentreceipt', 'finance', 'financepayment', 'income_categories', 'expenditure_categories',
            'online_stores', 'offline_stores'
        ]

        # 先尝试删除所有可能的外键约束
        #print("删除外键约束...")
        #try:
        #    cursor.execute("ALTER TABLE IF EXISTS user_roles DROP CONSTRAINT IF EXISTS user_roles_user_id_fkey CASCADE")
        #    cursor.execute("ALTER TABLE IF EXISTS user_roles DROP CONSTRAINT IF EXISTS user_roles_role_id_fkey CASCADE")
        #    cursor.execute("ALTER TABLE IF EXISTS role_permissions DROP CONSTRAINT IF EXISTS role_permissions_role_id_fkey CASCADE")
        #    cursor.execute("ALTER TABLE IF EXISTS role_permissions DROP CONSTRAINT IF EXISTS role_permissions_permission_id_fkey CASCADE")
        #except Exception as e:
        #    print(f"删除外键约束时出现警告（可忽略）: {e}")

        # 强制删除表
        for table in tables_to_drop:
            try:
                cursor.execute(f'DROP TABLE IF EXISTS {table} CASCADE')
                print(f"  ✅ 删除表 {table}")
            except Exception as e:
                print(f"  ⚠️  删除表 {table} 时出现警告: {e}")

        # 额外确保financepayment表被删除
        #try:
        #    cursor.execute('DROP TABLE IF EXISTS financepayment CASCADE')
        #    print("  ✅ 强制删除 financepayment 表")
        #except Exception as e:
        #    print(f"  ⚠️  强制删除 financepayment 表时出现警告: {e}")

        print("✅ 删除旧数据表完成")

        # 关闭自动提交，开始事务
        conn.autocommit = False
        # 1. 创建用户表
        print("1. 创建用户表...")
        cursor.execute('''
            CREATE TABLE users (
                id SERIAL PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                phone VARCHAR(20) UNIQUE,
                role VARCHAR(10) DEFAULT '3',
                status INTEGER DEFAULT 1,
                last_login TIMESTAMP,
                created_at TIMESTAMP,
                updated_at TIMESTAMP,
                is_deleted INTEGER DEFAULT 0
            )
        ''')

        # 2. 创建角色表
        print("2. 创建角色表...")
        cursor.execute('''
            CREATE TABLE roles (
                id SERIAL PRIMARY KEY,
                name VARCHAR(50) UNIQUE NOT NULL,
                description TEXT,
                status INTEGER DEFAULT 1,
                created_at TIMESTAMP,
                updated_at TIMESTAMP,
                is_deleted INTEGER DEFAULT 0
            )
        ''')
        
        # 3. 创建权限表
        print("3. 创建权限表...")
        cursor.execute('''
            CREATE TABLE permissions (
                id SERIAL PRIMARY KEY,
                code VARCHAR(50) UNIQUE NOT NULL,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                type VARCHAR(20) NOT NULL,
                parent_code VARCHAR(50),
                created_at TIMESTAMP,
                updated_at TIMESTAMP
            )
        ''')

        # 4. 创建角色权限关联表
        print("4. 创建角色权限关联表...")
        cursor.execute('''
            CREATE TABLE role_permissions (
                id SERIAL PRIMARY KEY,
                role_id INTEGER NOT NULL,
                permission_id INTEGER NOT NULL,
                created_at TIMESTAMP,
                FOREIGN KEY (role_id) REFERENCES roles (id) ON DELETE CASCADE,
                FOREIGN KEY (permission_id) REFERENCES permissions (id) ON DELETE CASCADE,
                UNIQUE(role_id, permission_id)
            )
        ''')

        # 5. 创建用户角色关联表
        print("5. 创建用户角色关联表...")
        cursor.execute('''
            CREATE TABLE user_roles (
                id SERIAL PRIMARY KEY,
                user_id INTEGER NOT NULL,
                role_id INTEGER NOT NULL,
                created_at TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
                FOREIGN KEY (role_id) REFERENCES roles (id) ON DELETE CASCADE,
                UNIQUE(user_id, role_id)
            )
        ''')

        # 6. 创建订单列表表
        print("6. 创建订单列表表...")
        cursor.execute('''
            CREATE TABLE orderlist (
                id SERIAL PRIMARY KEY,
                date VARCHAR(20),
                shop VARCHAR(100),
                order_no VARCHAR(50),
                sale_price FLOAT,
                box_fee FLOAT,
                delivery_fee FLOAT,
                actual_payment FLOAT,
                purchase_price FLOAT,
                product_profit FLOAT,
                total_profit FLOAT,
                screenshot VARCHAR(255),
                isdelete INTEGER DEFAULT 0
            )
        ''')

        # 7. 创建回款记录表
        print("7. 创建回款记录表...")
        cursor.execute('''
            CREATE TABLE paymentreceipt (
                id SERIAL PRIMARY KEY,
                date VARCHAR(20),
                shop VARCHAR(100),
                amount FLOAT,
                is_deleted INTEGER DEFAULT 0
            )
        ''')

        # 8. 创建财务表
        print("8. 创建财务表...")
        cursor.execute('''
            CREATE TABLE finance (
                id SERIAL PRIMARY KEY,
                shop VARCHAR(100),
                record_date VARCHAR(20),
                type VARCHAR(50),
                category VARCHAR(50),
                amount FLOAT,
                description VARCHAR(255),
                created_at TIMESTAMP,
                updated_at TIMESTAMP,
                screenshot VARCHAR(255),
                is_reimbursed INTEGER DEFAULT 1,
                reimbursed_status INTEGER DEFAULT 0,
                reimbursed_at TIMESTAMP DEFAULT NULL,
                is_deleted INTEGER DEFAULT 0
            )
        ''')

        # 9. 创建回款登记表
        print("9. 创建回款登记表...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS financepayment (
                id SERIAL PRIMARY KEY,
                shop VARCHAR(100),
                record_date VARCHAR(20),
                category VARCHAR(50),
                amount FLOAT,
                created_at TIMESTAMP,
                updated_at TIMESTAMP,
                screenshot VARCHAR(255),
                is_deleted INTEGER DEFAULT 0
            )
        ''')

        # 10. 创建收入分类表
        print("10. 创建收入分类表...")
        cursor.execute('''
            CREATE TABLE income_categories (
                id SERIAL PRIMARY KEY,
                name VARCHAR(100),
                status INTEGER DEFAULT 1,
                isdelete INTEGER DEFAULT 0,
                created_at TIMESTAMP,
                updated_at TIMESTAMP
            )
        ''')

        # 11. 创建支出分类表
        print("11. 创建支出分类表...")
        cursor.execute('''
            CREATE TABLE expenditure_categories (
                id SERIAL PRIMARY KEY,
                name VARCHAR(100),
                status INTEGER DEFAULT 1,
                isdelete INTEGER DEFAULT 0,
                created_at TIMESTAMP,
                updated_at TIMESTAMP
            )
        ''')

        # 12. 创建线上店铺表
        print("12. 创建线上店铺表...")
        cursor.execute('''
            CREATE TABLE online_stores (
                id SERIAL PRIMARY KEY,
                name VARCHAR(100),
                status INTEGER DEFAULT 1,
                isdelete INTEGER DEFAULT 0,
                created_at TIMESTAMP,
                updated_at TIMESTAMP
            )
        ''')

        # 13. 创建线下店铺表
        print("13. 创建线下店铺表...")
        cursor.execute('''
            CREATE TABLE offline_stores (
                id SERIAL PRIMARY KEY,
                name VARCHAR(100),
                status INTEGER DEFAULT 1,
                isdelete INTEGER DEFAULT 0,
                created_at TIMESTAMP,
                updated_at TIMESTAMP
            )
        ''')

        # 14. 插入初始角色数据
        print("14. 插入初始角色数据...")
        roles_data = [
            (1, '超级管理员', '拥有系统所有权限的超级管理员', 1),
            (2, '管理员', '拥有大部分管理权限的管理员', 1),
            (3, '普通用户', '普通用户角色，权限有限', 1)
        ]

        now = datetime.now()
        for role in roles_data:
            cursor.execute('''
                INSERT INTO roles (id, name, description, status, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s)
            ''', role + (now, now))

        # 15. 插入初始权限数据
        print("15. 插入初始权限数据...")
        permissions_data = [
            # 菜单权限
            (1, 'MENU_HOME', '首页', '首页菜单访问权限', 'menu', None),
            (2, 'MENU_SYSTEM', '系统管理', '系统管理菜单访问权限', 'menu', None),
            (3, 'MENU_USER', '用户管理', '用户管理菜单访问权限', 'menu', 'MENU_SYSTEM'),
            (4, 'MENU_ROLE', '角色管理', '角色管理菜单访问权限', 'menu', 'MENU_SYSTEM'),
            (5, 'MENU_PERMISSION_MANAGER', '权限管理', '权限管理菜单访问权限', 'menu', 'MENU_SYSTEM'),
            (6, 'MENU_BACKUP', '数据备份', '数据备份菜单访问权限', 'menu','MENU_SYSTEM'),
            (7, 'MENU_ORDER', '订单管理', '订单管理菜单访问权限', 'menu', None),
            (8, 'MENU_ORDERLIST', '订单列表', '订单列表菜单访问权限', 'menu', 'MENU_ORDER'),
            (9, 'MENU_PAYMENTRECEIPT', '回款管理', '回款管理菜单访问权限', 'menu', 'MENU_ORDER'),
            (10, 'MENU_ORDERSTAT', '订单统计', '订单统计菜单访问权限', 'menu', 'MENU_ORDER'),
            (11, 'MENU_FINANCE', '财务管理', '财务管理菜单访问权限', 'menu', None),
            (12, 'MENU_RECORD', '收支登记', '收支登记菜单访问权限', 'menu', 'MENU_FINANCE'),
            (13, 'MENU_REPORT', '报表管理', '报表管理菜单访问权限', 'menu', 'MENU_FINANCE'),
            (14, 'MENU_REIMBURSE', '报销管理', '报销管理菜单访问权限', 'menu', 'MENU_FINANCE'),
            (15, 'MENU_FINANCEPAYMENT', '回款登记', '回款登记菜单访问权限', 'menu', 'MENU_FINANCE'),
            (16, 'MENU_FINANCECALENDAR', '收支日历', '收支日历菜单访问权限', 'menu', 'MENU_FINANCE'),
            (17, 'MENU_ORDERCALENDAR', '订单日历', '订单日历菜单访问权限', 'menu', 'MENU_ORDER'),

            # 基础设置菜单权限
            (18, 'MENU_SETTING', '基础设置', '基础设置菜单访问权限', 'menu', None),
            (19, 'MENU_INCOME', '收入设置', '收入设置菜单访问权限', 'menu', 'MENU_SETTING'),
            (20, 'MENU_EXPENDITURE', '支出设置', '支出设置菜单访问权限', 'menu', 'MENU_SETTING'),
            (21, 'MENU_STORE_ONLINE', '线上店铺', '线上店铺菜单访问权限', 'menu', 'MENU_SETTING'),
            (22, 'MENU_STORE_OFFLINE', '线下店铺', '线下店铺菜单访问权限', 'menu', 'MENU_SETTING'),

            # 用户管理按钮权限
            (23, 'USER_ADD', '新增用户', '新增用户按钮权限', 'button', 'MENU_USER'),
            (24, 'USER_EDIT', '编辑用户', '编辑用户按钮权限', 'button', 'MENU_USER'),
            (25, 'USER_DELETE', '删除用户', '删除用户按钮权限', 'button', 'MENU_USER'),
            (26, 'USER_BATCH_DELETE', '批量删除用户', '批量删除用户按钮权限', 'button', 'MENU_USER'),
            (27, 'USER_STATUS', '用户状态切换', '用户状态切换按钮权限', 'button', 'MENU_USER'),

            # 角色管理按钮权限
            (28, 'ROLE_ADD', '新增角色', '新增角色按钮权限', 'button', 'MENU_ROLE'),
            (29, 'ROLE_EDIT', '编辑角色', '编辑角色按钮权限', 'button', 'MENU_ROLE'),
            (30, 'ROLE_DELETE', '删除角色', '删除角色按钮权限', 'button', 'MENU_ROLE'),
            (31, 'ROLE_BATCH_DELETE', '批量删除角色', '批量删除角色按钮权限', 'button', 'MENU_ROLE'),
            (32, 'ROLE_PERMISSION', '角色权限管理', '角色权限管理按钮权限', 'button', 'MENU_ROLE'),
            (33, 'ROLE_STATUS', '角色状态切换', '角色状态切换按钮权限', 'button', 'MENU_ROLE'),

            # 权限管理按钮权限
            (34, 'PERMISSION_ADD', '添加权限', '添加权限按钮权限', 'button', 'MENU_PERMISSION_MANAGER'),
            (35, 'PERMISSION_EDIT', '编辑权限', '编辑权限按钮权限', 'button', 'MENU_PERMISSION_MANAGER'),
            (36, 'PERMISSION_DELETE', '删除权限', '删除权限按钮权限', 'button', 'MENU_PERMISSION_MANAGER'),
            (37, 'PERMISSION_BATCH_DELETE', '批量删除权限', '批量删除权限按钮权限', 'button', 'MENU_PERMISSION_MANAGER'),

            # 订单管理按钮权限
            (38, 'ORDER_ADD', '新增订单', '新增订单按钮权限', 'button', 'MENU_ORDERLIST'),
            (39, 'ORDER_EDIT', '编辑订单', '编辑订单按钮权限', 'button', 'MENU_ORDERLIST'),
            (40, 'ORDER_DELETE', '删除订单', '删除订单按钮权限', 'button', 'MENU_ORDERLIST'),
            (41, 'ORDER_BATCH_DELETE', '批量删除订单', '批量删除订单按钮权限', 'button', 'MENU_ORDERLIST'),

            # 回款管理按钮权限
            (42, 'PAYMENT_ADD', '新增回款', '新增回款按钮权限', 'button', 'MENU_PAYMENTRECEIPT'),
            (43, 'PAYMENT_EDIT', '编辑回款', '编辑回款按钮权限', 'button', 'MENU_PAYMENTRECEIPT'),
            (44, 'PAYMENT_DELETE', '删除回款', '删除回款按钮权限', 'button', 'MENU_PAYMENTRECEIPT'),
            (45, 'PAYMENT_BATCH_DELETE', '批量删除回款', '批量删除回款按钮权限', 'button', 'MENU_PAYMENTRECEIPT'),

            # 财务管理按钮权限
            (46, 'FINANCE_ADD', '新增财务记录', '新增财务记录按钮权限', 'button', 'MENU_RECORD'),
            (47, 'FINANCE_EDIT', '编辑财务记录', '编辑财务记录按钮权限', 'button', 'MENU_RECORD'),
            (48, 'FINANCE_DELETE', '删除财务记录', '删除财务记录按钮权限', 'button', 'MENU_RECORD'),
            (49, 'FINANCE_BATCH_DELETE', '批量删除财务记录', '批量删除财务记录按钮权限', 'button', 'MENU_RECORD'),

            # 回款登记按钮权限
            (50, 'FINANCEPAYMENT_ADD', '添加权限', '添加权限按钮权限', 'button', 'MENU_FINANCEPAYMENT'),
            (51, 'FINANCEPAYMENT_EDIT', '编辑权限', '编辑权限按钮权限', 'button', 'MENU_FINANCEPAYMENT'),
            (52, 'FINANCEPAYMENT_DELETE', '删除权限', '删除权限按钮权限', 'button', 'MENU_FINANCEPAYMENT'),
            (53, 'FINANCEPAYMENT_BATCH_DELETE', '批量删除权限', '批量删除权限按钮权限', 'button', 'MENU_FINANCEPAYMENT'),

            # 报销管理按钮权限
            (54, 'REIMBURSE_AUDIT', '审核报销记录', '审核报销记录按钮权限', 'button', 'MENU_REIMBURSE'),
            (55, 'REIMBURSE_UNAUDIT', '反审核报销记录', '反审核报销记录按钮权限', 'button', 'MENU_REIMBURSE'),
            (56, 'REIMBURSE_BATCH_AUDIT', '批量审核报销记录', '批量审核报销记录按钮权限', 'button', 'MENU_REIMBURSE'),
            (57, 'REIMBURSE_BATCH_UNAUDIT', '批量反审核报销记录', '批量反审核报销记录按钮权限', 'button', 'MENU_REIMBURSE'),

            # 收入设置按钮权限
            (58, 'INCOME_ADD', '新增收入', '新增收入按钮权限', 'button', 'MENU_INCOME'),
            (59, 'INCOME_EDIT', '编辑收入', '编辑收入按钮权限', 'button', 'MENU_INCOME'),
            (60, 'INCOME_DELETE', '删除收入', '删除收入按钮权限', 'button', 'MENU_INCOME'),
            (61, 'INCOME_BATCH_DELETE', '批量删除收入', '批量删除收入按钮权限', 'button', 'MENU_INCOME'),
            (62, 'INCOME_STATUS', '收入状态切换', '收入状态切换按钮权限', 'button', 'MENU_INCOME'),

            # 支出设置按钮权限
            (63, 'EXPENDITURE_ADD', '新增支出', '新增支出按钮权限', 'button', 'MENU_EXPENDITURE'),
            (64, 'EXPENDITURE_EDIT', '编辑支出', '编辑支出按钮权限', 'button', 'MENU_EXPENDITURE'),
            (65, 'EXPENDITURE_DELETE', '删除支出', '删除支出按钮权限', 'button', 'MENU_EXPENDITURE'),
            (66, 'EXPENDITURE_BATCH_DELETE', '批量删除支出', '批量删除支出按钮权限', 'button', 'MENU_EXPENDITURE'),
            (67, 'EXPENDITURE_STATUS', '支出状态切换', '支出状态切换按钮权限', 'button', 'MENU_EXPENDITURE'),

            # 线上店铺按钮权限
            (68, 'STORE_ONLINE_ADD', '新增线上店铺', '新增线上店铺按钮权限', 'button', 'MENU_STORE_ONLINE'),
            (69, 'STORE_ONLINE_EDIT', '编辑线上店铺', '编辑线上店铺按钮权限', 'button', 'MENU_STORE_ONLINE'),
            (70, 'STORE_ONLINE_DELETE', '删除线上店铺', '删除线上店铺按钮权限', 'button', 'MENU_STORE_ONLINE'),
            (71, 'STORE_ONLINE_BATCH_DELETE', '批量删除线上店铺', '批量删除线上店铺按钮权限', 'button', 'MENU_STORE_ONLINE'),
            (72, 'STORE_ONLINE_STATUS', '线上店铺状态切换', '线下店铺状态切换按钮权限', 'button', 'MENU_STORE_ONLINE'),

            # 线下店铺按钮权限
            (73, 'STORE_OFFLINE_ADD', '新增线下店铺', '新增线下店铺按钮权限', 'button', 'MENU_STORE_OFFLINE'),
            (74, 'STORE_OFFLINE_EDIT', '编辑线下店铺', '编辑线下店铺按钮权限', 'button', 'MENU_STORE_OFFLINE'),
            (75, 'STORE_OFFLINE_DELETE', '删除线下店铺', '删除线下店铺按钮权限', 'button', 'MENU_STORE_OFFLINE'),
            (76, 'STORE_OFFLINE_BATCH_DELETE', '批量删除线下店铺', '批量删除线下店铺按钮权限', 'button', 'MENU_STORE_OFFLINE'),
            (77, 'STORE_OFFLINE_STATUS', '线下店铺状态切换', '线下店铺状态切换按钮权限', 'button', 'MENU_STORE_OFFLINE'),

            # 新增 回款登记按钮权限
            (78, 'FINANCEPAYMENT_BATCH_ADD', '批量新增回款登记', '批量新增回款登记按钮权限', 'button', 'MENU_FINANCEPAYMENT'),

        ]

        for perm in permissions_data:
            cursor.execute('''
                INSERT INTO permissions (id, code, name, description, type, parent_code, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            ''', perm + (now, now))

        # 16. 给超级管理员角色分配所有权限
        print("16. 给超级管理员角色分配所有权限...")
        for perm_id in range(1, 78):  
            cursor.execute('''
                INSERT INTO role_permissions (role_id, permission_id, created_at)
                VALUES (%s, %s, %s)
            ''', (1, perm_id, now))

        # 17. 给管理员角色分配部分权限（除了删除权限和权限管理）
        print("17. 给管理员角色分配部分权限...")
        admin_permissions = [1, 2, 3, 4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 23, 24, 28, 34, 35, 38, 39, 42, 43, 46, 47, 48, 49, 50, 54, 55, 59, 60, 64, 65, 69, 74]  # 不包括删除和批量删除权限，不包括权限管理，包括备份权限（除了删除备份）
        for perm_id in admin_permissions:
            cursor.execute('''
                INSERT INTO role_permissions (role_id, permission_id, created_at)
                VALUES (%s, %s, %s)
            ''', (2, perm_id, now))

        # 18. 给普通用户角色分配基础权限
        print("18. 给普通用户角色分配基础权限...")
        user_permissions = [1]  # 只有首页权限
        for perm_id in user_permissions:
            cursor.execute('''
                INSERT INTO role_permissions (role_id, permission_id, created_at)
                VALUES (%s, %s, %s)
            ''', (3, perm_id, now))

        # 19. 创建初始用户
        print("19. 创建初始用户...")
        users_data = [
            ('admin', hash_password('123456'), '13724098353', '1', 1),
            ('user', hash_password('123456'), '13800000001', '3', 1),
            ('guest', hash_password('123456'), '13800000002', '3', 1)
        ]

        for user in users_data:
            cursor.execute('''
                INSERT INTO users (username, password, phone, role, status, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            ''', user + (now, now))

        # 20. 分配用户角色
        print("20. 分配用户角色...")
        # admin用户分配超级管理员角色
        cursor.execute('''
            INSERT INTO user_roles (user_id, role_id, created_at)
            VALUES (%s, %s, %s)
        ''', (1, 1, now))

        # user用户分配普通用户角色
        cursor.execute('''
            INSERT INTO user_roles (user_id, role_id, created_at)
            VALUES (%s, %s, %s)
        ''', (2, 3, now))

        # guest用户分配普通用户角色
        cursor.execute('''
            INSERT INTO user_roles (user_id, role_id, created_at)
            VALUES (%s, %s, %s)
        ''', (3, 3, now))

        # 21. 插入初始收入分类数据
        print("21. 插入初始收入分类数据...")
        income_categories_data = [
            ('小程序', 1, 0),
            ('外卖', 1, 0),
            ('美团团购', 1, 0),
            ('抖音团购', 1, 0)
        ]

        for category in income_categories_data:
            cursor.execute('''
                INSERT INTO income_categories (name, status, isdelete, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s)
            ''', category + (now, now))

        # 22. 插入初始支出分类数据
        print("22. 插入初始支出分类数据...")
        expenditure_categories_data = [
            ('进货', 1, 0),
            ('水电费', 1, 0),
            ('租金', 1, 0),
            ('员工工资', 1, 0),
            ('兼职工资', 1, 0),
            ('杂项', 1, 0)
        ]

        for category in expenditure_categories_data:
            cursor.execute('''
                INSERT INTO expenditure_categories (name, status, isdelete, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s)
            ''', category + (now, now))

        # 23. 插入初始线上店铺数据
        print("23. 插入初始线上店铺数据...")
        online_stores_data = [
            ('肯德基', 1, 0),
            ('古茗', 1, 0)
        ]

        for store in online_stores_data:
            cursor.execute('''
                INSERT INTO online_stores (name, status, isdelete, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s)
            ''', store + (now, now))

        # 24. 插入初始线下店铺数据
        print("24. 插入初始线下店铺数据...")
        offline_stores_data = [
            ('谷麦夏', 1, 0)
        ]

        for store in offline_stores_data:
            cursor.execute('''
                INSERT INTO offline_stores (name, status, isdelete, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s)
            ''', store + (now, now))

        # 提交事务
        conn.commit()
        print("\n✅ 数据库初始化完成！")
        
        # 验证数据
        print("\n=== 验证数据 ===")
        cursor.execute('SELECT COUNT(*) FROM users')
        user_count = cursor.fetchone()[0]
        print(f"用户数量: {user_count}")

        cursor.execute('SELECT COUNT(*) FROM roles')
        role_count = cursor.fetchone()[0]
        print(f"角色数量: {role_count}")

        cursor.execute('SELECT COUNT(*) FROM permissions')
        perm_count = cursor.fetchone()[0]
        print(f"权限数量: {perm_count}")

        cursor.execute('SELECT COUNT(*) FROM role_permissions')
        role_perm_count = cursor.fetchone()[0]
        print(f"角色权限关联数量: {role_perm_count}")

        cursor.execute('SELECT COUNT(*) FROM user_roles')
        user_role_count = cursor.fetchone()[0]
        print(f"用户角色关联数量: {user_role_count}")

        cursor.execute('SELECT COUNT(*) FROM orderlist')
        order_count = cursor.fetchone()[0]
        print(f"订单表记录数量: {order_count}")

        cursor.execute('SELECT COUNT(*) FROM paymentreceipt')
        payment_count = cursor.fetchone()[0]
        print(f"回款表记录数量: {payment_count}")

        cursor.execute('SELECT COUNT(*) FROM finance')
        finance_count = cursor.fetchone()[0]
        print(f"财务表记录数量: {finance_count}")

        cursor.execute('SELECT COUNT(*) FROM income_categories')
        income_cat_count = cursor.fetchone()[0]
        print(f"收入分类表记录数量: {income_cat_count}")

        cursor.execute('SELECT COUNT(*) FROM expenditure_categories')
        expenditure_cat_count = cursor.fetchone()[0]
        print(f"支出分类表记录数量: {expenditure_cat_count}")

        cursor.execute('SELECT COUNT(*) FROM online_stores')
        online_store_count = cursor.fetchone()[0]
        print(f"线上店铺表记录数量: {online_store_count}")

        cursor.execute('SELECT COUNT(*) FROM offline_stores')
        offline_store_count = cursor.fetchone()[0]
        print(f"线下店铺表记录数量: {offline_store_count}")

        print("\n=== 初始账号信息 ===")
        print("超级管理员账号:")
        print("  用户名: admin")
        print("  密码: 123456")
        print("  手机号: 13724098353")
        print("  角色: 超级管理员")
        print()
        print("普通用户账号:")
        print("  用户名: user, guest")
        print("  密码: 123456")
        print("  手机号: 13800000001, 13800000002")
        print("  角色: 普通用户")

        # 26. 添加表注释
        print("26. 添加表注释...")
        table_comments = {
            'users': '用户账号信息',
            'roles': '系统角色配置',
            'permissions': '权限配置',
            'user_roles': '用户角色关联',
            'role_permissions': '角色权限关联',
            'orderlist': '订单记录',
            'paymentreceipt': '回款记录',
            'finance': '收支记录',
            'financepayment': '回款登记',
            'income_categories': '收入分类',
            'expenditure_categories': '支出分类',
            'online_stores': '线上店铺',
            'offline_stores': '线下店铺'
        }

        for table_name, comment in table_comments.items():
            try:
                cursor.execute(f"COMMENT ON TABLE {table_name} IS %s", (comment,))
            except Exception as e:
                print(f"为表 {table_name} 添加注释失败: {str(e)}")

        print("\n✅ 数据库初始化完成！")
        print("数据库表结构创建成功，初始数据插入完成。")

    except Exception as e:
        if conn:
            conn.rollback()
        print(f"❌ 数据库初始化失败: {str(e)}")
        raise
    finally:
        if conn:
            conn.close()

if __name__ == '__main__':
    init_database()
