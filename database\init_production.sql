-- 生产环境数据库初始化脚本
-- 此脚本用于在生产环境中初始化PostgreSQL数据库

-- 设置数据库编码和区域设置
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

-- 创建数据库（如果不存在）
SELECT 'CREATE DATABASE postgresql_vue_db'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'postgresql_vue_db')\gexec

-- 连接到目标数据库
\c postgresql_vue_db;

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- 设置时区
SET timezone = 'Asia/Shanghai';

-- 创建数据库用户（如果不存在）
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_user WHERE usename = 'app_user') THEN
        CREATE USER app_user WITH PASSWORD 'your_app_password_here';
    END IF;
END
$$;

-- 授予权限
GRANT CONNECT ON DATABASE postgresql_vue_db TO app_user;
GRANT USAGE ON SCHEMA public TO app_user;
GRANT CREATE ON SCHEMA public TO app_user;

-- 创建备份用户
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_user WHERE usename = 'backup_user') THEN
        CREATE USER backup_user WITH PASSWORD 'your_backup_password_here';
    END IF;
END
$$;

-- 授予备份用户只读权限
GRANT CONNECT ON DATABASE postgresql_vue_db TO backup_user;
GRANT USAGE ON SCHEMA public TO backup_user;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO backup_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO backup_user;

-- 性能优化设置
-- 这些设置应该根据服务器配置进行调整

-- 共享缓冲区（建议设置为系统内存的25%）
-- ALTER SYSTEM SET shared_buffers = '1GB';

-- 有效缓存大小（建议设置为系统内存的75%）
-- ALTER SYSTEM SET effective_cache_size = '3GB';

-- 工作内存（建议根据并发连接数调整）
-- ALTER SYSTEM SET work_mem = '4MB';

-- 维护工作内存
-- ALTER SYSTEM SET maintenance_work_mem = '256MB';

-- 检查点设置
-- ALTER SYSTEM SET checkpoint_completion_target = 0.9;
-- ALTER SYSTEM SET wal_buffers = '16MB';

-- 连接设置
-- ALTER SYSTEM SET max_connections = 100;

-- 日志设置
ALTER SYSTEM SET log_destination = 'stderr';
ALTER SYSTEM SET logging_collector = on;
ALTER SYSTEM SET log_directory = 'pg_log';
ALTER SYSTEM SET log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log';
ALTER SYSTEM SET log_rotation_age = '1d';
ALTER SYSTEM SET log_rotation_size = '100MB';
ALTER SYSTEM SET log_min_duration_statement = '1000ms';
ALTER SYSTEM SET log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h ';
ALTER SYSTEM SET log_checkpoints = on;
ALTER SYSTEM SET log_connections = on;
ALTER SYSTEM SET log_disconnections = on;
ALTER SYSTEM SET log_lock_waits = on;

-- 统计信息收集
ALTER SYSTEM SET track_activities = on;
ALTER SYSTEM SET track_counts = on;
ALTER SYSTEM SET track_io_timing = on;
ALTER SYSTEM SET track_functions = 'all';

-- 自动清理设置
ALTER SYSTEM SET autovacuum = on;
ALTER SYSTEM SET autovacuum_max_workers = 3;
ALTER SYSTEM SET autovacuum_naptime = '1min';

-- 重新加载配置
SELECT pg_reload_conf();

-- 创建监控视图
CREATE OR REPLACE VIEW v_database_stats AS
SELECT 
    datname as database_name,
    numbackends as active_connections,
    xact_commit as transactions_committed,
    xact_rollback as transactions_rolled_back,
    blks_read as blocks_read,
    blks_hit as blocks_hit,
    round((blks_hit::float / (blks_hit + blks_read)) * 100, 2) as cache_hit_ratio,
    tup_returned as tuples_returned,
    tup_fetched as tuples_fetched,
    tup_inserted as tuples_inserted,
    tup_updated as tuples_updated,
    tup_deleted as tuples_deleted,
    stats_reset
FROM pg_stat_database 
WHERE datname = current_database();

-- 创建表空间使用情况视图
CREATE OR REPLACE VIEW v_table_sizes AS
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as total_size,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) as index_size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- 创建慢查询视图（需要pg_stat_statements扩展）
CREATE OR REPLACE VIEW v_slow_queries AS
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 20;

-- 创建连接信息视图
CREATE OR REPLACE VIEW v_active_connections AS
SELECT 
    pid,
    usename,
    application_name,
    client_addr,
    client_port,
    backend_start,
    state,
    query_start,
    query
FROM pg_stat_activity 
WHERE state = 'active' 
AND pid <> pg_backend_pid();

-- 创建备份函数
CREATE OR REPLACE FUNCTION backup_database(backup_path text)
RETURNS text AS $$
DECLARE
    backup_file text;
    cmd text;
BEGIN
    backup_file := backup_path || '/db_backup_' || to_char(now(), 'YYYYMMDD_HH24MISS') || '.sql';
    cmd := 'pg_dump -h localhost -U postgres -d postgresql_vue_db > ' || backup_file;
    
    -- 注意：在生产环境中，应该使用更安全的方式执行系统命令
    -- 这里只是示例，实际使用时建议通过外部脚本处理
    
    RETURN backup_file;
END;
$$ LANGUAGE plpgsql;

-- 创建数据清理函数
CREATE OR REPLACE FUNCTION cleanup_old_data(days_to_keep integer DEFAULT 90)
RETURNS text AS $$
DECLARE
    deleted_count integer;
    result_text text;
BEGIN
    -- 清理旧的日志记录（如果有日志表的话）
    -- DELETE FROM logs WHERE created_at < now() - interval '%s days', days_to_keep;
    -- GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    result_text := 'Cleanup completed. Deleted ' || deleted_count || ' old records.';
    
    -- 更新统计信息
    ANALYZE;
    
    RETURN result_text;
END;
$$ LANGUAGE plpgsql;

-- 授予应用用户对视图和函数的权限
GRANT SELECT ON v_database_stats TO app_user;
GRANT SELECT ON v_table_sizes TO app_user;
GRANT SELECT ON v_active_connections TO app_user;
GRANT EXECUTE ON FUNCTION cleanup_old_data(integer) TO app_user;

-- 创建定期维护任务（需要pg_cron扩展，可选）
-- SELECT cron.schedule('database-maintenance', '0 2 * * *', 'SELECT cleanup_old_data(90);');

COMMIT;
