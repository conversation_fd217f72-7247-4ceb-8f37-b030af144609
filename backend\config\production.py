import os
from datetime import timedelta

class ProductionConfig:
    """生产环境配置类"""

    # 基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY')
    if not SECRET_KEY:
        raise ValueError("SECRET_KEY environment variable is required in production")

    # PostgreSQL数据库配置
    DATABASE_CONFIG = {
        'host': os.environ.get('DB_HOST', 'database'),
        'port': os.environ.get('DB_PORT', '5432'),
        'database': os.environ.get('DB_NAME', 'postgresql_vue_db'),
        'user': os.environ.get('DB_USER', 'postgres'),
        'password': os.environ.get('DB_PASSWORD')
    }

    if not DATABASE_CONFIG['password']:
        raise ValueError("DB_PASSWORD environment variable is required in production")

    # 数据库连接字符串
    DATABASE_URL = f"postgresql://{DATABASE_CONFIG['user']}:{DATABASE_CONFIG['password']}@{DATABASE_CONFIG['host']}:{DATABASE_CONFIG['port']}/{DATABASE_CONFIG['database']}"

    # JWT配置
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY')
    if not JWT_SECRET_KEY:
        raise ValueError("JWT_SECRET_KEY environment variable is required in production")
    
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=24)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)

    # CORS配置 - 生产环境应该限制具体域名
    DOMAIN = os.environ.get('DOMAIN', 'localhost')
    CORS_ORIGINS = [
        f"https://{DOMAIN}",
        f"http://{DOMAIN}",
        f"https://www.{DOMAIN}",
        f"http://www.{DOMAIN}"
    ]

    # 调试模式 - 生产环境关闭
    DEBUG = False

    # 文件上传配置
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    UPLOAD_FOLDER = '/app/static/uploads'
    ALLOWED_EXTENSIONS = {'xlsx', 'xls', 'csv', 'png', 'jpg', 'jpeg', 'gif'}

    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FILE = '/app/logs/app.log'

    # 缓存配置
    REDIS_URL = os.environ.get('REDIS_URL', 'redis://redis:6379/0')
    CACHE_TIMEOUT = int(os.environ.get('CACHE_TIMEOUT', '3600'))

    # 安全配置
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)

    # 邮件配置（可选）
    MAIL_SERVER = os.environ.get('SMTP_HOST')
    MAIL_PORT = int(os.environ.get('SMTP_PORT', '587'))
    MAIL_USE_TLS = os.environ.get('SMTP_USE_TLS', 'true').lower() == 'true'
    MAIL_USERNAME = os.environ.get('SMTP_USER')
    MAIL_PASSWORD = os.environ.get('SMTP_PASSWORD')

    # 备份配置
    BACKUP_SCHEDULE = os.environ.get('BACKUP_SCHEDULE', '0 2 * * *')
    BACKUP_RETENTION_DAYS = int(os.environ.get('BACKUP_RETENTION_DAYS', '30'))

    # 监控配置
    ENABLE_MONITORING = os.environ.get('ENABLE_MONITORING', 'false').lower() == 'true'
