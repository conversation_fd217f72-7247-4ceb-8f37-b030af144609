@echo off
REM Windows 部署脚本
REM 使用方法: deploy.bat [操作] [参数]

setlocal enabledelayedexpansion

REM 颜色定义（Windows 10+）
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

REM 日志函数
:log_info
echo %BLUE%[INFO]%NC% %~1
goto :eof

:log_success
echo %GREEN%[SUCCESS]%NC% %~1
goto :eof

:log_warning
echo %YELLOW%[WARNING]%NC% %~1
goto :eof

:log_error
echo %RED%[ERROR]%NC% %~1
goto :eof

REM 检查Docker是否安装
:check_docker
docker --version >nul 2>&1
if errorlevel 1 (
    call :log_error "Docker 未安装，请先安装 Docker Desktop"
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    call :log_error "Docker Compose 未安装，请先安装 Docker Compose"
    exit /b 1
)
goto :eof

REM 检查环境文件
:check_env_file
if not exist .env (
    call :log_warning ".env 文件不存在，正在从 .env.example 创建..."
    copy .env.example .env >nul
    call :log_warning "请编辑 .env 文件并设置正确的配置值"
    exit /b 1
)
goto :eof

REM 构建镜像
:build_images
call :log_info "开始构建 Docker 镜像..."
docker-compose build --no-cache
if errorlevel 1 (
    call :log_error "镜像构建失败"
    exit /b 1
)
call :log_success "镜像构建完成"
goto :eof

REM 启动服务
:start_services
call :log_info "启动服务..."
docker-compose up -d
if errorlevel 1 (
    call :log_error "服务启动失败"
    exit /b 1
)

call :log_info "等待服务启动..."
timeout /t 10 /nobreak >nul

call :check_services_health
goto :eof

REM 停止服务
:stop_services
call :log_info "停止服务..."
docker-compose down
call :log_success "服务已停止"
goto :eof

REM 重启服务
:restart_services
call :log_info "重启服务..."
docker-compose restart
timeout /t 10 /nobreak >nul
call :check_services_health
goto :eof

REM 检查服务健康状态
:check_services_health
call :log_info "检查服务健康状态..."

REM 检查数据库
docker-compose exec -T database pg_isready -U postgres >nul 2>&1
if errorlevel 1 (
    call :log_error "数据库服务异常"
) else (
    call :log_success "数据库服务正常"
)

REM 检查后端
curl -f http://localhost:5000/api/health >nul 2>&1
if errorlevel 1 (
    call :log_error "后端服务异常"
) else (
    call :log_success "后端服务正常"
)

REM 检查前端
curl -f http://localhost >nul 2>&1
if errorlevel 1 (
    call :log_error "前端服务异常"
) else (
    call :log_success "前端服务正常"
)
goto :eof

REM 查看日志
:view_logs
if "%~1"=="" (
    docker-compose logs -f
) else (
    docker-compose logs -f %~1
)
goto :eof

REM 数据库备份
:backup_database
call :log_info "开始数据库备份..."

if not exist backups mkdir backups

REM 生成备份文件名
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "backup_file=backups\db_backup_%YYYY%%MM%%DD%_%HH%%Min%%Sec%.sql"

REM 执行备份
docker-compose exec -T database pg_dump -U postgres postgresql_vue_db > "%backup_file%"
if errorlevel 1 (
    call :log_error "数据库备份失败"
    exit /b 1
)
call :log_success "数据库备份完成: %backup_file%"
goto :eof

REM 显示帮助信息
:show_help
echo 项目部署脚本使用说明:
echo.
echo 用法: %~nx0 [操作] [参数]
echo.
echo 操作:
echo   build           构建 Docker 镜像
echo   up              启动所有服务
echo   down            停止所有服务
echo   restart         重启所有服务
echo   logs [service]  查看日志 (可选指定服务名)
echo   backup          备份数据库
echo   health          检查服务健康状态
echo   help            显示此帮助信息
echo.
echo 示例:
echo   %~nx0 build        # 构建镜像
echo   %~nx0 up           # 启动服务
echo   %~nx0 logs backend # 查看后端日志
echo   %~nx0 backup       # 备份数据库
goto :eof

REM 主函数
:main
call :check_docker

set "action=%~1"
set "param=%~2"

if "%action%"=="build" (
    call :check_env_file
    call :build_images
) else if "%action%"=="up" (
    call :check_env_file
    call :start_services
) else if "%action%"=="down" (
    call :stop_services
) else if "%action%"=="restart" (
    call :restart_services
) else if "%action%"=="logs" (
    call :view_logs "%param%"
) else if "%action%"=="backup" (
    call :backup_database
) else if "%action%"=="health" (
    call :check_services_health
) else if "%action%"=="help" (
    call :show_help
) else if "%action%"=="" (
    call :show_help
) else (
    call :log_error "未知操作: %action%"
    call :show_help
    exit /b 1
)

goto :eof

REM 执行主函数
call :main %*
