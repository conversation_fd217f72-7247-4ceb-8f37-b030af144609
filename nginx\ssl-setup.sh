#!/bin/bash

# SSL证书自动配置脚本
# 使用 Let's Encrypt 免费证书

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -lt 2 ]; then
    echo "使用方法: $0 <域名> <邮箱>"
    echo "示例: $0 yourdomain.com <EMAIL>"
    exit 1
fi

DOMAIN=$1
EMAIL=$2

log_info "开始为域名 $DOMAIN 配置 SSL 证书"

# 检查是否安装了 certbot
if ! command -v certbot &> /dev/null; then
    log_info "安装 certbot..."
    
    # Ubuntu/Debian
    if command -v apt-get &> /dev/null; then
        sudo apt-get update
        sudo apt-get install -y certbot python3-certbot-nginx
    # CentOS/RHEL
    elif command -v yum &> /dev/null; then
        sudo yum install -y epel-release
        sudo yum install -y certbot python3-certbot-nginx
    else
        log_error "不支持的操作系统，请手动安装 certbot"
        exit 1
    fi
fi

# 创建 webroot 目录
sudo mkdir -p /var/www/certbot

# 临时 Nginx 配置（用于验证域名）
cat > /tmp/temp_nginx.conf << EOF
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN;
    
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }
    
    location / {
        return 200 'OK';
        add_header Content-Type text/plain;
    }
}
EOF

# 备份原配置
if [ -f /etc/nginx/sites-available/default ]; then
    sudo cp /etc/nginx/sites-available/default /etc/nginx/sites-available/default.backup
fi

# 应用临时配置
sudo cp /tmp/temp_nginx.conf /etc/nginx/sites-available/temp
sudo ln -sf /etc/nginx/sites-available/temp /etc/nginx/sites-enabled/temp

# 测试配置并重启 Nginx
sudo nginx -t && sudo systemctl reload nginx

log_info "获取 SSL 证书..."

# 获取证书
sudo certbot certonly \
    --webroot \
    --webroot-path=/var/www/certbot \
    --email $EMAIL \
    --agree-tos \
    --no-eff-email \
    -d $DOMAIN \
    -d www.$DOMAIN

if [ $? -eq 0 ]; then
    log_success "SSL 证书获取成功"
else
    log_error "SSL 证书获取失败"
    exit 1
fi

# 创建 SSL 目录
sudo mkdir -p /etc/nginx/ssl

# 创建证书软链接
sudo ln -sf /etc/letsencrypt/live/$DOMAIN/fullchain.pem /etc/nginx/ssl/fullchain.pem
sudo ln -sf /etc/letsencrypt/live/$DOMAIN/privkey.pem /etc/nginx/ssl/privkey.pem

# 应用正式的 Nginx 配置
sudo cp nginx/nginx.conf /etc/nginx/sites-available/$DOMAIN

# 更新配置中的域名
sudo sed -i "s/yourdomain.com/$DOMAIN/g" /etc/nginx/sites-available/$DOMAIN

# 启用站点
sudo ln -sf /etc/nginx/sites-available/$DOMAIN /etc/nginx/sites-enabled/$DOMAIN

# 删除临时配置
sudo rm -f /etc/nginx/sites-enabled/temp
sudo rm -f /etc/nginx/sites-available/temp

# 测试配置
sudo nginx -t

if [ $? -eq 0 ]; then
    sudo systemctl reload nginx
    log_success "Nginx 配置更新成功"
else
    log_error "Nginx 配置测试失败"
    exit 1
fi

# 设置自动续期
log_info "设置证书自动续期..."

# 创建续期脚本
cat > /tmp/renew-cert.sh << 'EOF'
#!/bin/bash
certbot renew --quiet --post-hook "systemctl reload nginx"
EOF

sudo cp /tmp/renew-cert.sh /usr/local/bin/renew-cert.sh
sudo chmod +x /usr/local/bin/renew-cert.sh

# 添加到 crontab
(sudo crontab -l 2>/dev/null; echo "0 12 * * * /usr/local/bin/renew-cert.sh") | sudo crontab -

log_success "SSL 证书配置完成！"
log_info "证书将在每天中午12点自动检查续期"
log_info "您的网站现在可以通过 https://$DOMAIN 访问"

# 显示证书信息
log_info "证书信息:"
sudo certbot certificates
