# 生产环境配置文件示例
# 复制此文件为 .env 并修改相应的值

# 数据库配置
DB_HOST=database
DB_PORT=5432
DB_NAME=postgresql_vue_db
DB_USER=postgres
DB_PASSWORD=your_strong_password_here

# 应用密钥（请使用强密码）
SECRET_KEY=your_very_strong_secret_key_here
JWT_SECRET_KEY=your_jwt_secret_key_here

# 域名配置
DOMAIN=yourdomain.com
SSL_EMAIL=<EMAIL>

# 备份配置
BACKUP_SCHEDULE=0 2 * * *  # 每天凌晨2点备份
BACKUP_RETENTION_DAYS=30

# 邮件配置（可选）
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_password
SMTP_USE_TLS=true

# 监控配置（可选）
ENABLE_MONITORING=true
LOG_LEVEL=INFO

# 文件上传限制
MAX_UPLOAD_SIZE=10MB
ALLOWED_EXTENSIONS=xlsx,xls,csv

# 缓存配置
REDIS_URL=redis://redis:6379/0
CACHE_TIMEOUT=3600
