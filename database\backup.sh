#!/bin/bash

# 数据库备份脚本
# 支持本地备份和远程备份

set -e

# 配置
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-5432}
DB_NAME=${DB_NAME:-postgresql_vue_db}
DB_USER=${DB_USER:-postgres}
BACKUP_DIR=${BACKUP_DIR:-./backups}
RETENTION_DAYS=${RETENTION_DAYS:-30}

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 创建备份目录
create_backup_dir() {
    if [ ! -d "$BACKUP_DIR" ]; then
        mkdir -p "$BACKUP_DIR"
        log_info "创建备份目录: $BACKUP_DIR"
    fi
}

# 执行数据库备份
backup_database() {
    local backup_type=$1
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_file="$BACKUP_DIR/db_${backup_type}_${timestamp}.sql"
    
    log_info "开始${backup_type}备份..."
    
    case $backup_type in
        "full")
            # 完整备份
            pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
                --verbose --clean --no-owner --no-privileges \
                --format=custom > "$backup_file.custom"
            
            # 同时创建SQL格式备份
            pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
                --verbose --clean --no-owner --no-privileges > "$backup_file"
            ;;
        "schema")
            # 仅备份结构
            pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
                --verbose --clean --no-owner --no-privileges --schema-only > "$backup_file"
            ;;
        "data")
            # 仅备份数据
            pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
                --verbose --no-owner --no-privileges --data-only > "$backup_file"
            ;;
        *)
            log_error "未知的备份类型: $backup_type"
            return 1
            ;;
    esac
    
    if [ $? -eq 0 ]; then
        # 压缩备份文件
        gzip "$backup_file"
        backup_file="${backup_file}.gz"
        
        # 计算文件大小
        local file_size=$(du -h "$backup_file" | cut -f1)
        
        log_success "${backup_type}备份完成: $backup_file (大小: $file_size)"
        
        # 验证备份文件
        if [ -f "$backup_file" ] && [ -s "$backup_file" ]; then
            log_success "备份文件验证通过"
        else
            log_error "备份文件验证失败"
            return 1
        fi
    else
        log_error "${backup_type}备份失败"
        return 1
    fi
}

# 清理旧备份
cleanup_old_backups() {
    log_info "清理 $RETENTION_DAYS 天前的备份文件..."
    
    local deleted_count=0
    
    # 查找并删除旧文件
    while IFS= read -r -d '' file; do
        rm "$file"
        deleted_count=$((deleted_count + 1))
        log_info "删除旧备份: $(basename "$file")"
    done < <(find "$BACKUP_DIR" -name "db_*.sql.gz" -type f -mtime +$RETENTION_DAYS -print0)
    
    if [ $deleted_count -gt 0 ]; then
        log_success "清理完成，删除了 $deleted_count 个旧备份文件"
    else
        log_info "没有需要清理的旧备份文件"
    fi
}

# 恢复数据库
restore_database() {
    local backup_file=$1
    
    if [ -z "$backup_file" ]; then
        log_error "请指定备份文件路径"
        return 1
    fi
    
    if [ ! -f "$backup_file" ]; then
        log_error "备份文件不存在: $backup_file"
        return 1
    fi
    
    log_warning "即将恢复数据库，这将覆盖现有数据！"
    read -p "确认继续？(y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "开始恢复数据库..."
        
        # 检查文件是否压缩
        if [[ "$backup_file" == *.gz ]]; then
            gunzip -c "$backup_file" | psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME"
        else
            psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" < "$backup_file"
        fi
        
        if [ $? -eq 0 ]; then
            log_success "数据库恢复完成"
        else
            log_error "数据库恢复失败"
            return 1
        fi
    else
        log_info "取消恢复操作"
    fi
}

# 列出备份文件
list_backups() {
    log_info "备份文件列表:"
    
    if [ ! -d "$BACKUP_DIR" ]; then
        log_warning "备份目录不存在: $BACKUP_DIR"
        return 1
    fi
    
    local backup_files=($(find "$BACKUP_DIR" -name "db_*.sql.gz" -type f | sort -r))
    
    if [ ${#backup_files[@]} -eq 0 ]; then
        log_warning "没有找到备份文件"
        return 1
    fi
    
    printf "%-30s %-15s %-20s\n" "文件名" "大小" "修改时间"
    printf "%-30s %-15s %-20s\n" "------------------------------" "---------------" "--------------------"
    
    for file in "${backup_files[@]}"; do
        local filename=$(basename "$file")
        local filesize=$(du -h "$file" | cut -f1)
        local modtime=$(stat -c %y "$file" | cut -d' ' -f1,2 | cut -d'.' -f1)
        printf "%-30s %-15s %-20s\n" "$filename" "$filesize" "$modtime"
    done
}

# 测试数据库连接
test_connection() {
    log_info "测试数据库连接..."
    
    if pg_isready -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" > /dev/null 2>&1; then
        log_success "数据库连接正常"
        
        # 显示数据库信息
        local db_version=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT version();" | head -1 | xargs)
        local db_size=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT pg_size_pretty(pg_database_size('$DB_NAME'));" | xargs)
        
        log_info "数据库版本: $db_version"
        log_info "数据库大小: $db_size"
    else
        log_error "数据库连接失败"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    echo "数据库备份脚本使用说明:"
    echo ""
    echo "用法: $0 [操作] [参数]"
    echo ""
    echo "操作:"
    echo "  full                完整备份（结构+数据）"
    echo "  schema              仅备份结构"
    echo "  data                仅备份数据"
    echo "  restore <file>      恢复数据库"
    echo "  list                列出备份文件"
    echo "  cleanup             清理旧备份"
    echo "  test                测试数据库连接"
    echo "  help                显示此帮助信息"
    echo ""
    echo "环境变量:"
    echo "  DB_HOST             数据库主机 (默认: localhost)"
    echo "  DB_PORT             数据库端口 (默认: 5432)"
    echo "  DB_NAME             数据库名称 (默认: postgresql_vue_db)"
    echo "  DB_USER             数据库用户 (默认: postgres)"
    echo "  BACKUP_DIR          备份目录 (默认: ./backups)"
    echo "  RETENTION_DAYS      备份保留天数 (默认: 30)"
    echo ""
    echo "示例:"
    echo "  $0 full             # 完整备份"
    echo "  $0 restore backup.sql.gz  # 恢复备份"
    echo "  $0 cleanup          # 清理旧备份"
}

# 主函数
main() {
    local action=$1
    local param=$2
    
    case $action in
        "full"|"schema"|"data")
            create_backup_dir
            test_connection
            backup_database "$action"
            cleanup_old_backups
            ;;
        "restore")
            test_connection
            restore_database "$param"
            ;;
        "list")
            list_backups
            ;;
        "cleanup")
            cleanup_old_backups
            ;;
        "test")
            test_connection
            ;;
        "help"|"")
            show_help
            ;;
        *)
            log_error "未知操作: $action"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
