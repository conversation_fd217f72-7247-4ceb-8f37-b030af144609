# 📦 项目部署文件说明

本项目提供了完整的线上部署解决方案，包含Docker容器化部署和传统部署两种方式。

## 📁 部署相关文件结构

```
postgresql-vue-project/
├── 部署配置文件
│   ├── docker-compose.yml          # Docker编排配置
│   ├── .env.example               # 环境变量模板
│   ├── deploy.sh                  # Linux部署脚本
│   ├── deploy.bat                 # Windows部署脚本
│   └── quick-deploy.sh            # 一键部署脚本
│
├── 前端部署
│   ├── frontend/Dockerfile        # 前端Docker镜像
│   └── frontend/nginx.conf        # 前端Nginx配置
│
├── 后端部署
│   ├── backend/Dockerfile         # 后端Docker镜像
│   └── backend/config/production.py # 生产环境配置
│
├── 数据库配置
│   ├── database/init_production.sql # 生产数据库初始化
│   └── database/backup.sh         # 数据库备份脚本
│
├── Nginx配置
│   ├── nginx/nginx.conf           # 生产环境Nginx配置
│   └── nginx/ssl-setup.sh         # SSL证书自动配置
│
└── 文档
    ├── DEPLOYMENT_GUIDE.md        # 详细部署指南
    └── README_DEPLOYMENT.md       # 本文件
```

## 🚀 快速开始

### 方式一：一键部署（推荐新手）

```bash
# 下载并运行一键部署脚本
curl -fsSL https://raw.githubusercontent.com/your-repo/main/quick-deploy.sh | bash -s -- <你的Git仓库URL>

# 或者手动执行
git clone <你的Git仓库URL>
cd postgresql-vue-project
chmod +x quick-deploy.sh
./quick-deploy.sh <你的Git仓库URL>
```

### 方式二：Docker部署（推荐）

```bash
# 1. 克隆项目
git clone <你的Git仓库URL>
cd postgresql-vue-project

# 2. 配置环境变量
cp .env.example .env
nano .env  # 编辑配置

# 3. 部署
chmod +x deploy.sh
./deploy.sh build  # 构建镜像
./deploy.sh up     # 启动服务
./deploy.sh health # 检查状态
```

### 方式三：传统部署

详细步骤请参考 [DEPLOYMENT_GUIDE.md](./DEPLOYMENT_GUIDE.md)

## 🔧 核心配置文件说明

### 1. docker-compose.yml
- 定义了完整的服务栈：数据库、后端、前端、Redis
- 配置了服务间的网络通信
- 设置了数据持久化卷
- 包含健康检查配置

### 2. .env.example
- 包含所有必要的环境变量模板
- 数据库连接配置
- 应用密钥配置
- 域名和SSL配置

### 3. deploy.sh / deploy.bat
- 跨平台部署脚本
- 支持构建、启动、停止、重启、备份等操作
- 包含服务健康检查
- 自动化日志查看

### 4. Dockerfile文件
- **backend/Dockerfile**: Python Flask应用容器化
- **frontend/Dockerfile**: Vue.js应用多阶段构建，使用Nginx提供静态文件服务

### 5. Nginx配置
- **frontend/nginx.conf**: 容器内Nginx配置
- **nginx/nginx.conf**: 生产环境独立Nginx配置
- 包含SSL、Gzip、缓存、安全头等优化配置

## 🛠️ 部署脚本使用说明

### deploy.sh 命令

```bash
./deploy.sh build           # 构建Docker镜像
./deploy.sh up              # 启动所有服务
./deploy.sh down            # 停止所有服务
./deploy.sh restart         # 重启所有服务
./deploy.sh logs [service]  # 查看日志
./deploy.sh backup          # 备份数据库
./deploy.sh health          # 检查服务健康状态
./deploy.sh help            # 显示帮助信息
```

### backup.sh 命令

```bash
./database/backup.sh full      # 完整备份
./database/backup.sh schema    # 仅备份结构
./database/backup.sh data      # 仅备份数据
./database/backup.sh list      # 列出备份文件
./database/backup.sh cleanup   # 清理旧备份
./database/backup.sh test      # 测试数据库连接
```

## 🔒 安全配置

### 1. 环境变量安全
- 所有敏感信息通过环境变量配置
- 生产环境必须使用强密码
- JWT密钥使用随机生成的强密钥

### 2. 数据库安全
- 创建专用应用用户，限制权限
- 创建只读备份用户
- 启用连接日志和慢查询日志

### 3. Web安全
- 配置安全HTTP头
- 启用HTTPS和HSTS
- 配置防火墙规则
- 实施API限流

## 📊 监控和维护

### 1. 日志管理
- 应用日志：通过Docker logs查看
- Nginx日志：/var/log/nginx/
- 数据库日志：PostgreSQL容器内

### 2. 性能监控
- 使用 `docker stats` 查看容器资源使用
- 数据库性能视图：v_database_stats, v_slow_queries
- 系统监控：htop, iostat等工具

### 3. 备份策略
- 自动化数据库备份
- 备份文件压缩和清理
- 支持本地和远程备份

## 🔧 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   sudo netstat -tlnp | grep :80
   sudo netstat -tlnp | grep :5000
   ```

2. **Docker权限问题**
   ```bash
   sudo usermod -aG docker $USER
   # 重新登录生效
   ```

3. **服务启动失败**
   ```bash
   ./deploy.sh logs        # 查看所有日志
   docker-compose ps       # 查看服务状态
   ```

4. **数据库连接失败**
   ```bash
   ./database/backup.sh test  # 测试数据库连接
   ```

## 📋 部署检查清单

- [ ] 服务器环境准备（Docker, Git, Nginx等）
- [ ] 项目代码克隆
- [ ] 环境变量配置（.env文件）
- [ ] Docker镜像构建成功
- [ ] 所有服务启动正常
- [ ] 数据库初始化完成
- [ ] 前端页面可以访问
- [ ] 后端API正常响应
- [ ] 域名DNS解析配置
- [ ] SSL证书配置（生产环境）
- [ ] 防火墙规则配置
- [ ] 备份策略设置
- [ ] 监控配置

## 📞 获取帮助

如果在部署过程中遇到问题：

1. 查看 [DEPLOYMENT_GUIDE.md](./DEPLOYMENT_GUIDE.md) 详细指南
2. 检查相关日志文件
3. 确认配置文件正确性
4. 参考故障排除部分

---

**部署愉快！** 🎉
