#!/bin/bash

# 项目部署脚本
# 使用方法: ./deploy.sh [环境] [操作]
# 环境: dev, prod
# 操作: build, up, down, restart, logs, backup

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
}

# 检查环境文件
check_env_file() {
    if [ ! -f .env ]; then
        log_warning ".env 文件不存在，正在从 .env.example 创建..."
        cp .env.example .env
        log_warning "请编辑 .env 文件并设置正确的配置值"
        exit 1
    fi
}

# 构建镜像
build_images() {
    log_info "开始构建 Docker 镜像..."
    docker-compose build --no-cache
    log_success "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    docker-compose up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    check_services_health
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    docker-compose down
    log_success "服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启服务..."
    docker-compose restart
    sleep 10
    check_services_health
}

# 检查服务健康状态
check_services_health() {
    log_info "检查服务健康状态..."
    
    # 检查数据库
    if docker-compose exec -T database pg_isready -U postgres > /dev/null 2>&1; then
        log_success "数据库服务正常"
    else
        log_error "数据库服务异常"
    fi
    
    # 检查后端
    if curl -f http://localhost:5000/api/health > /dev/null 2>&1; then
        log_success "后端服务正常"
    else
        log_error "后端服务异常"
    fi
    
    # 检查前端
    if curl -f http://localhost > /dev/null 2>&1; then
        log_success "前端服务正常"
    else
        log_error "前端服务异常"
    fi
}

# 查看日志
view_logs() {
    local service=$1
    if [ -z "$service" ]; then
        docker-compose logs -f
    else
        docker-compose logs -f "$service"
    fi
}

# 数据库备份
backup_database() {
    log_info "开始数据库备份..."
    
    # 创建备份目录
    mkdir -p ./backups
    
    # 生成备份文件名
    backup_file="./backups/db_backup_$(date +%Y%m%d_%H%M%S).sql"
    
    # 执行备份
    docker-compose exec -T database pg_dump -U postgres postgresql_vue_db > "$backup_file"
    
    if [ $? -eq 0 ]; then
        log_success "数据库备份完成: $backup_file"
    else
        log_error "数据库备份失败"
        exit 1
    fi
}

# 数据库恢复
restore_database() {
    local backup_file=$1
    
    if [ -z "$backup_file" ]; then
        log_error "请指定备份文件路径"
        exit 1
    fi
    
    if [ ! -f "$backup_file" ]; then
        log_error "备份文件不存在: $backup_file"
        exit 1
    fi
    
    log_warning "即将恢复数据库，这将覆盖现有数据！"
    read -p "确认继续？(y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "开始恢复数据库..."
        docker-compose exec -T database psql -U postgres -d postgresql_vue_db < "$backup_file"
        log_success "数据库恢复完成"
    else
        log_info "取消恢复操作"
    fi
}

# 清理系统
cleanup() {
    log_info "清理 Docker 系统..."
    docker system prune -f
    docker volume prune -f
    log_success "清理完成"
}

# 显示帮助信息
show_help() {
    echo "项目部署脚本使用说明:"
    echo ""
    echo "用法: $0 [操作] [参数]"
    echo ""
    echo "操作:"
    echo "  build           构建 Docker 镜像"
    echo "  up              启动所有服务"
    echo "  down            停止所有服务"
    echo "  restart         重启所有服务"
    echo "  logs [service]  查看日志 (可选指定服务名)"
    echo "  backup          备份数据库"
    echo "  restore <file>  恢复数据库"
    echo "  health          检查服务健康状态"
    echo "  cleanup         清理 Docker 系统"
    echo "  help            显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 build        # 构建镜像"
    echo "  $0 up           # 启动服务"
    echo "  $0 logs backend # 查看后端日志"
    echo "  $0 backup       # 备份数据库"
}

# 主函数
main() {
    local action=$1
    local param=$2
    
    # 检查 Docker
    check_docker
    
    case $action in
        "build")
            check_env_file
            build_images
            ;;
        "up")
            check_env_file
            start_services
            ;;
        "down")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "logs")
            view_logs "$param"
            ;;
        "backup")
            backup_database
            ;;
        "restore")
            restore_database "$param"
            ;;
        "health")
            check_services_health
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|"")
            show_help
            ;;
        *)
            log_error "未知操作: $action"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
