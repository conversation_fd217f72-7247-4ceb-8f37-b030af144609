# 🚀 项目线上部署完整指南

## 📋 目录
1. [部署前准备](#部署前准备)
2. [服务器环境配置](#服务器环境配置)
3. [Docker部署方式](#docker部署方式)
4. [传统部署方式](#传统部署方式)
5. [域名和SSL配置](#域名和ssl配置)
6. [监控和维护](#监控和维护)
7. [故障排除](#故障排除)

## 🛠️ 部署前准备

### 1. 服务器要求

**最低配置：**
- CPU: 2核
- 内存: 4GB
- 硬盘: 40GB SSD
- 带宽: 3Mbps
- 操作系统: Ubuntu 20.04+ / CentOS 7+

**推荐配置：**
- CPU: 4核
- 内存: 8GB
- 硬盘: 80GB SSD
- 带宽: 5Mbps

### 2. 域名准备
- 购买域名（如：yourdomain.com）
- 配置DNS解析到服务器IP
- 准备SSL证书（推荐Let's Encrypt免费证书）

### 3. 必要软件
- Docker & Docker Compose
- Git
- Nginx（如果不使用Docker内置）

## 🐳 Docker部署方式（推荐）

### 1. 安装Docker

**Ubuntu/Debian:**
```bash
# 更新包索引
sudo apt update

# 安装必要的包
sudo apt install apt-transport-https ca-certificates curl gnupg lsb-release

# 添加Docker官方GPG密钥
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# 添加Docker仓库
echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# 安装Docker
sudo apt update
sudo apt install docker-ce docker-ce-cli containerd.io docker-compose-plugin

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 将当前用户添加到docker组
sudo usermod -aG docker $USER
```

**CentOS/RHEL:**
```bash
# 安装必要的包
sudo yum install -y yum-utils

# 添加Docker仓库
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo

# 安装Docker
sudo yum install docker-ce docker-ce-cli containerd.io docker-compose-plugin

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 将当前用户添加到docker组
sudo usermod -aG docker $USER
```

### 2. 部署项目

```bash
# 1. 克隆项目到服务器
git clone <your-repository-url>
cd postgresql-vue-project

# 2. 配置环境变量
cp .env.example .env
nano .env  # 编辑配置文件

# 3. 构建并启动服务
chmod +x deploy.sh
./deploy.sh build
./deploy.sh up

# 4. 检查服务状态
./deploy.sh health
```

### 3. 环境变量配置

编辑 `.env` 文件：

```env
# 数据库配置
DB_HOST=database
DB_PORT=5432
DB_NAME=postgresql_vue_db
DB_USER=postgres
DB_PASSWORD=your_strong_password_here

# 应用密钥
SECRET_KEY=your_very_strong_secret_key_here
JWT_SECRET_KEY=your_jwt_secret_key_here

# 域名配置
DOMAIN=yourdomain.com
SSL_EMAIL=<EMAIL>
```

## 🔧 传统部署方式

### 1. 安装Node.js和Python

**安装Node.js:**
```bash
# 使用NodeSource仓库安装Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装pnpm
npm install -g pnpm
```

**安装Python:**
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3 python3-pip python3-venv

# CentOS/RHEL
sudo yum install python3 python3-pip
```

### 2. 安装PostgreSQL

```bash
# Ubuntu/Debian
sudo apt install postgresql postgresql-contrib

# CentOS/RHEL
sudo yum install postgresql-server postgresql-contrib
sudo postgresql-setup initdb
```

### 3. 部署后端

```bash
# 1. 进入后端目录
cd backend

# 2. 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 3. 安装依赖
pip install -r requirements.txt

# 4. 配置数据库
# 编辑 config/config.py 文件

# 5. 初始化数据库
cd database
python init_database.py

# 6. 启动后端服务
cd ..
python app.py
```

### 4. 部署前端

```bash
# 1. 进入前端目录
cd frontend

# 2. 安装依赖
pnpm install

# 3. 构建项目
pnpm build

# 4. 配置Nginx
sudo cp nginx/nginx.conf /etc/nginx/sites-available/yourdomain.com
sudo ln -s /etc/nginx/sites-available/yourdomain.com /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 🔒 域名和SSL配置

### 1. 配置DNS解析

在域名管理面板中添加A记录：
```
类型: A
名称: @
值: 你的服务器IP地址
TTL: 600

类型: A
名称: www
值: 你的服务器IP地址
TTL: 600
```

### 2. 获取SSL证书

使用提供的脚本自动配置：

```bash
# 给脚本执行权限
chmod +x nginx/ssl-setup.sh

# 运行SSL配置脚本
./nginx/ssl-setup.sh yourdomain.com <EMAIL>
```

或手动配置：

```bash
# 安装certbot
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# 设置自动续期
sudo crontab -e
# 添加以下行：
# 0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 监控和维护

### 1. 日志查看

```bash
# Docker方式
./deploy.sh logs          # 查看所有服务日志
./deploy.sh logs backend  # 查看后端日志
./deploy.sh logs frontend # 查看前端日志

# 传统方式
tail -f /var/log/nginx/access.log  # Nginx访问日志
tail -f /var/log/nginx/error.log   # Nginx错误日志
```

### 2. 数据库备份

```bash
# 使用备份脚本
chmod +x database/backup.sh

# 完整备份
./database/backup.sh full

# 设置定时备份
crontab -e
# 添加以下行（每天凌晨2点备份）：
# 0 2 * * * /path/to/your/project/database/backup.sh full
```

### 3. 性能监控

```bash
# 查看系统资源使用情况
htop

# 查看Docker容器状态
docker stats

# 查看数据库连接
docker exec -it postgresql_vue_db psql -U postgres -d postgresql_vue_db -c "SELECT * FROM v_active_connections;"
```

## 🔧 故障排除

### 1. 常见问题

**服务无法启动：**
```bash
# 检查端口占用
sudo netstat -tlnp | grep :80
sudo netstat -tlnp | grep :5000

# 检查Docker服务
sudo systemctl status docker
docker-compose ps
```

**数据库连接失败：**
```bash
# 检查数据库状态
docker exec -it postgresql_vue_db pg_isready -U postgres

# 查看数据库日志
docker logs postgresql_vue_db
```

**前端页面无法访问：**
```bash
# 检查Nginx状态
sudo systemctl status nginx

# 测试Nginx配置
sudo nginx -t

# 查看Nginx日志
sudo tail -f /var/log/nginx/error.log
```

### 2. 性能优化

**数据库优化：**
- 定期执行 `VACUUM` 和 `ANALYZE`
- 监控慢查询
- 适当调整连接池大小

**应用优化：**
- 启用Gzip压缩
- 配置静态资源缓存
- 使用CDN加速

### 3. 安全加固

```bash
# 更新系统
sudo apt update && sudo apt upgrade

# 配置防火墙
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow http
sudo ufw allow https

# 禁用root登录
sudo nano /etc/ssh/sshd_config
# 设置 PermitRootLogin no
sudo systemctl restart ssh
```

## 🚀 一键部署脚本

为了简化部署过程，我们提供了一键部署脚本：

```bash
# 下载并运行一键部署脚本
curl -fsSL https://raw.githubusercontent.com/your-repo/main/quick-deploy.sh | bash
```

或者手动执行：

```bash
# 给脚本执行权限
chmod +x quick-deploy.sh

# 运行一键部署
./quick-deploy.sh
```

## 📞 技术支持

如果在部署过程中遇到问题，请：

1. 查看相关日志文件
2. 检查配置文件是否正确
3. 确认网络连接和防火墙设置
4. 参考本文档的故障排除部分

## 📋 部署检查清单

部署完成后，请检查以下项目：

- [ ] 服务器基础环境配置完成
- [ ] Docker和Docker Compose安装成功
- [ ] 项目代码克隆到服务器
- [ ] 环境变量配置正确
- [ ] 数据库初始化完成
- [ ] 前后端服务正常启动
- [ ] 域名DNS解析配置
- [ ] SSL证书配置成功
- [ ] Nginx反向代理配置
- [ ] 防火墙和安全设置
- [ ] 数据库备份策略
- [ ] 监控和日志配置

---

**祝您部署顺利！** 🎉
